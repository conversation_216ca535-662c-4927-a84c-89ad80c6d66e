#include "dev_id.h"
#include "sdkconfig.h"
#include "esp_log.h"
#include "esp_efuse.h"
#include "esp_mac.h"
#include "nvs_flash.h"
#include "nvs.h"

static const char *TAG = "DEV_ID";

/* 全局变量存储设备信息 */
static bool dev_id_initialized = false;
static uint16_t cached_device_id = 1; // 默认设备ID为1（有效范围内）
static uint8_t cached_device_mac[DEV_MAC_ADDR_LEN] = {0};

/**
 * @brief 内部函数：从NVS读取设备ID
 */
static esp_err_t read_device_id_from_nvs(uint16_t *device_id)
{
    nvs_handle_t nvs_handle;
    esp_err_t err = nvs_open("device_config", NVS_READONLY, &nvs_handle);
    *device_id = 1; // 设置默认设备ID为1（有效范围内）
    if (err != ESP_OK)
    {
        ESP_LOGE(TAG, "无法打开NVS存储: %s，使用默认设备ID: %d", esp_err_to_name(err), *device_id);
        return ESP_FAIL;
    }
    // 首次运行时：读取失败后会自动将默认ID写入NVS
    // 后续运行时：直接从NVS读取之前保存的ID
    // 重新烧录后：会重新写入默认ID到NVS
    err = nvs_get_u16(nvs_handle, "device_id", device_id);
    if (err != ESP_OK)
    {
        ESP_LOGW(TAG, "读取设备ID失败: %s，写入默认设备ID: %d", esp_err_to_name(err), *device_id);

        // 首次运行或重新烧录后，写入默认设备ID到NVS
        nvs_close(nvs_handle);
        err = nvs_open("device_config", NVS_READWRITE, &nvs_handle);
        if (err == ESP_OK)
        {
            err = nvs_set_u16(nvs_handle, "device_id", *device_id);
            if (err == ESP_OK)
            {
                err = nvs_commit(nvs_handle);
                ESP_LOGI(TAG, "默认设备ID已保存到NVS: %d", *device_id);
            }
            else
            {
                ESP_LOGE(TAG, "保存设备ID失败: %s", esp_err_to_name(err));
            }
        }
    }
    nvs_close(nvs_handle);
    return ESP_OK; // 总是返回成功，因为我们提供了默认值
}

/**
 * @brief 内部函数：从eFuse读取MAC地址
 */
static esp_err_t read_device_mac_from_efuse(uint8_t *device_mac)
{
    esp_err_t err = esp_efuse_mac_get_default(device_mac);
    if (err != ESP_OK)
    {
        ESP_LOGE(TAG, "Failed to get base MAC address: %s", esp_err_to_name(err));
    }
    return err;
}

// 公共API函数实现
esp_err_t dev_id_init(void)
{
    if (dev_id_initialized)
    {
        ESP_LOGW(TAG, "Device ID module already initialized");
        return ESP_OK;
    }

    esp_err_t ret;

    // 读取设备ID
    ret = read_device_id_from_nvs(&cached_device_id);
    if (ret == ESP_OK)
    {
        ESP_LOGI(TAG, "设备ID读取成功: %d", cached_device_id);
    }
    else
    {
        ESP_LOGW(TAG, "设备ID读取失败，使用默认值: %d", cached_device_id);
    }

    // 读取MAC地址
    ret = read_device_mac_from_efuse(cached_device_mac);
    if (ret == ESP_OK)
    {
        ESP_LOGI(TAG, "设备MAC读取成功: %02X:%02X:%02X:%02X:%02X:%02X",
                 cached_device_mac[0], cached_device_mac[1], cached_device_mac[2],
                 cached_device_mac[3], cached_device_mac[4], cached_device_mac[5]);
    }
    else
    {
        ESP_LOGE(TAG, "设备MAC读取失败: %s", esp_err_to_name(ret));
        return ret;
    }

    dev_id_initialized = true;
    ESP_LOGI(TAG, "Device ID module initialized successfully");
    return ESP_OK;
}

esp_err_t get_device_id(uint16_t *device_id)
{
    if (!dev_id_initialized)
    {
        ESP_LOGE(TAG, "Device ID module not initialized");
        return ESP_ERR_INVALID_STATE;
    }

    if (device_id == NULL)
    {
        ESP_LOGE(TAG, "Invalid parameter: device_id is NULL");
        return ESP_ERR_INVALID_ARG;
    }

    *device_id = cached_device_id;
    return ESP_OK;
}

esp_err_t get_device_mac(uint8_t *device_mac)
{
    if (!dev_id_initialized)
    {
        ESP_LOGE(TAG, "Device ID module not initialized");
        return ESP_ERR_INVALID_STATE;
    }

    if (device_mac == NULL)
    {
        ESP_LOGE(TAG, "Invalid parameter: device_mac is NULL");
        return ESP_ERR_INVALID_ARG;
    }

    for (int i = 0; i < DEV_MAC_ADDR_LEN; i++)
    {
        device_mac[i] = cached_device_mac[i];
    }
    return ESP_OK;
}

esp_err_t set_device_id(uint16_t device_id)
{
    if (!dev_id_initialized)
    {
        ESP_LOGE(TAG, "Device ID module not initialized");
        return ESP_ERR_INVALID_STATE;
    }

    // 验证设备ID范围 (1-255)
    if (device_id < 1 || device_id > 255)
    {
        ESP_LOGE(TAG, "Invalid device ID: %d (must be 1-255)", device_id);
        return ESP_ERR_INVALID_ARG;
    }

    // 打开NVS存储
    nvs_handle_t nvs_handle;
    esp_err_t err = nvs_open("device_config", NVS_READWRITE, &nvs_handle);
    if (err != ESP_OK)
    {
        ESP_LOGE(TAG, "Failed to open NVS storage: %s", esp_err_to_name(err));
        return ESP_FAIL;
    }

    // 保存新的设备ID到NVS
    err = nvs_set_u16(nvs_handle, "device_id", device_id);
    if (err != ESP_OK)
    {
        ESP_LOGE(TAG, "Failed to save device ID to NVS: %s", esp_err_to_name(err));
        nvs_close(nvs_handle);
        return ESP_FAIL;
    }

    // 提交更改
    err = nvs_commit(nvs_handle);
    if (err != ESP_OK)
    {
        ESP_LOGE(TAG, "Failed to commit NVS changes: %s", esp_err_to_name(err));
        nvs_close(nvs_handle);
        return ESP_FAIL;
    }

    nvs_close(nvs_handle);

    // 更新缓存的设备ID
    cached_device_id = device_id;

    ESP_LOGI(TAG, "Device ID successfully updated to: %d", device_id);
    return ESP_OK;
}
