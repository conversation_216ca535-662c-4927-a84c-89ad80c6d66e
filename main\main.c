/*
 * ESP32 BLE Mill Monitor Master - Main Application Entry Point
 *
 * This file contains the main application initialization and coordination
 * of all system components including BLE, WiFi, file system, and HTTP server.
 */

#include <stdio.h>
#include <string.h>
#include "esp_log.h"
#include "esp_err.h"
#include "esp_system.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"

// Component headers
#include "ble_master.h"
#include "wifi_ap.h"
#include "fs_mount.h"
#include "file_server.h"
#include "dev_id.h"

static const char *TAG = "MAIN";

// File system mount point
#define MOUNT_POINT "/spiffs"

void app_main(void)
{
    esp_err_t ret;

    // NVS（Non-Volatile Storage） 是一种非易失性存储系统，主要用于在设备掉电或重启后保留数据
    // 通常是以 key-value（键值对） 的形式存储数据, 保存配置参数（如 WiFi 名称、密码）
    ret = nvs_flash_init();
    if (ret == ESP_ERR_NVS_NO_FREE_PAGES || ret == ESP_ERR_NVS_NEW_VERSION_FOUND)
    {
        ESP_ERROR_CHECK(nvs_flash_erase());
        ret = nvs_flash_init();
    }
    ESP_ERROR_CHECK(ret);

    // 初始化设备ID模块
    ret = dev_id_init();
    if (ret != ESP_OK)
    {
        ESP_LOGE(TAG, "Device ID init failed: %s", esp_err_to_name(ret));
        return;
    }
    ESP_LOGI(TAG, "Device ID init success");

    // 初始化WiFi AP
    ret = wifi_init_ap();
    if (ret != ESP_OK)
    {
        ESP_LOGE(TAG, "WiFi AP init faild: %s", esp_err_to_name(ret));
        return;
    }
    ESP_LOGI(TAG, "WiFi AP init success");

    // 挂载文件系统
    const char *base_path = "/data";
    ret = mount_storage(base_path);
    if (ret != ESP_OK)
    {
        ESP_LOGE(TAG, "mount_storage failed: %s", esp_err_to_name(ret));
        return;
    }

    // 启动文件服务器
    ret = start_file_server(base_path);
    if (ret != ESP_OK)
    {
        ESP_LOGE(TAG, "File server start failed: %s", esp_err_to_name(ret));
        return;
    }

    // 初始化BLE Master功能（包含UART初始化）
    ret = ble_master_init();
    if (ret != ESP_OK)
    {
        ESP_LOGE(TAG, "BLE Master init failed: %s, system will restart in 5 seconds!", esp_err_to_name(ret));
        vTaskDelay(pdMS_TO_TICKS(5000));
        esp_restart();
        return;
    }

    // 启动BLE协议栈
    ret = ble_master_start();
    if (ret != ESP_OK)
    {
        ESP_LOGE(TAG, "BLE Master start failed:  %s", esp_err_to_name(ret));
        return;
    }

    // 主循环 - 系统监控
    while (1)
    {
        vTaskDelay(pdMS_TO_TICKS(10000)); // 每10秒检查一次
    }
}
