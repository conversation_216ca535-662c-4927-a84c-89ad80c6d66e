/*
 * WiFi AP 模式配置头文件
 */

#pragma once

#include "esp_err.h"

#ifdef __cplusplus
extern "C"
{
#endif

    /**
     * @brief 初始化WiFi AP模式并优化BLE共存
     *
     * 此函数初始化WiFi AP模式，包含以下优化：
     * - 开放网络（无需密码）便于访问
     * - 减少缓冲区大小以最小化内存使用
     * - 省电模式以更好地与BLE共存
     * - 选择信道6以减少2.4GHz干扰
     * - 20MHz带宽以最小化频谱使用
     * - 增加信标间隔以减少空中时间
     * - 平衡的共存偏好设置
     * - 最多2个并发连接以减少负载
     *
     * @return esp_err_t 成功时返回ESP_OK
     */
    esp_err_t wifi_init_ap(void);

#ifdef __cplusplus
}
#endif
