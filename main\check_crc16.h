/*
 * CRC16校验算法实现
 *
 * 提供CRC16-CCITT算法的计算和验证功能
 */

#ifndef CHECK_CRC16_H
#define CHECK_CRC16_H

#include <stdint.h>
#include <stddef.h>
#include <stdbool.h>

#ifdef __cplusplus
extern "C"
{
#endif

    /**
     * @brief 计算CRC16校验值
     *
     * 使用标准CRC16-CCITT算法计算校验值
     * 多项式：0x1021，初始值：0xFFFF
     *
     * @param data 数据指针
     * @param length 数据长度
     * @return uint16_t CRC16校验值
     */
    uint16_t calculate_crc16(const uint8_t *data, size_t length);

    /**
     * @brief 验证CRC16校验值
     *
     * 验证数据包的CRC16校验是否正确
     *
     * @param data 包含CRC16的完整数据包指针
     * @param length 数据包总长度（包括CRC16字段）
     * @param crc_offset CRC16字段在数据包中的偏移位置
     * @param little_endian 是否为小端序（true=小端，false=大端）
     * @return true 校验正确，false 校验失败
     */
    bool verify_crc16(const uint8_t *data, size_t length, size_t crc_offset, bool little_endian);

    /**
     * @brief 添加CRC16校验值到数据包
     *
     * 计算数据的CRC16并添加到指定位置
     *
     * @param data 数据包指针
     * @param data_length 数据长度（不包括CRC16字段）
     * @param crc_offset CRC16字段在数据包中的偏移位置
     * @param little_endian 是否为小端序（true=小端，false=大端）
     */
    void append_crc16(uint8_t *data, size_t data_length, size_t crc_offset, bool little_endian);

#ifdef __cplusplus
}
#endif

#endif /* CHECK_CRC16_H */
